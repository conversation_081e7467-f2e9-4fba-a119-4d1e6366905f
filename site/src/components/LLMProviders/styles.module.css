.providersSection {
  padding: 4rem 0;
  background-color: #f8f9fa;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.titleUnderline {
  width: 60px;
  height: 4px;
  background-color: var(--ifm-color-primary);
  margin: 0 auto;
  border-radius: 2px;
}

.sectionDescription {
  max-width: 600px;
  margin: 2rem auto 0;
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--ifm-color-emphasis-700);
  text-align: center;
}

.docsLink {
  color: var(--ifm-color-primary);
  text-decoration: none;
  font-weight: 600;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.docsLink:hover {
  color: var(--ifm-color-primary-dark);
  border-bottom-color: var(--ifm-color-primary-dark);
  text-decoration: none;
}

.providersGrid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.providerCol {
  flex: 0 0 auto;
  min-width: 180px;
  max-width: 200px;
}

.providerCard {
  background: white;
  border-radius: 12px;
  padding: 2.5rem 1.5rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.providerCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(108, 24, 153, 0.15);
  border-color: var(--ifm-color-primary-light);
}

.providerCard.inProgress {
  background: #f8f9fa;
  border-color: #dee2e6;
}

.providerCard.inProgress:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.logoContainer {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.providerLogo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.providerCard.inProgress .providerLogo {
  opacity: 0.6;
  filter: grayscale(100%) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.providerName {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ifm-color-emphasis-800);
  margin-bottom: 0.5rem;
}

.providerCard.inProgress .providerName {
  color: var(--ifm-color-emphasis-600);
}

.statusBadge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, #ffd700, #ffb300);
  color: #333;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media screen and (max-width: 996px) {
  .providersSection {
    padding: 3rem 0;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .providersGrid {
    gap: 1.5rem;
  }

  .providerCol {
    min-width: 160px;
    max-width: 180px;
  }

    .providerCard {
    padding: 2rem 1rem;
  }

  .logoContainer {
    width: 100px;
    height: 100px;
  }
}

@media screen and (max-width: 768px) {
  .providersSection {
    padding: 2rem 0;
  }

  .sectionTitle {
    font-size: 1.8rem;
  }

  .providersGrid {
    gap: 1rem;
  }

  .providerCol {
    min-width: 140px;
    max-width: 160px;
  }

    .providerCard {
    padding: 1.5rem 0.75rem;
  }

  .logoContainer {
    width: 80px;
    height: 80px;
  }

  .providerName {
    font-size: 0.9rem;
  }
}

@media screen and (max-width: 576px) {
  .providersGrid {
    justify-content: center;
  }

  .providerCol {
    min-width: 130px;
    max-width: 150px;
  }

  .providerCard {
    padding: 1rem 0.5rem;
  }

  .logoContainer {
    width: 70px;
    height: 70px;
  }

  .providerName {
    font-size: 0.85rem;
  }

  .statusBadge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }
}
