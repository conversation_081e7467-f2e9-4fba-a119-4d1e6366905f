---
id: httproute-inferencepool
title: HTTPRoute + InferencePool Guide
sidebar_position: 2
---

# HTTPRoute + InferencePool Guide

This guide demonstrates how to use InferencePool with standard Gateway API HTTPRoute for intelligent inference routing. This approach provides basic load balancing and endpoint selection capabilities for inference workloads.

## Prerequisites

Before starting, ensure you have:

1. **Kubernetes cluster** with Gateway API support
2. **Envoy Gateway** installed and configured
3. **Gateway API Inference Extension** installed

## Step 1: Install Gateway API Inference Extension

Install the Gateway API Inference Extension CRDs and controller:

```bash
kubectl apply -f https://github.com/kubernetes-sigs/gateway-api-inference-extension/releases/download/v0.4.0/manifests.yaml
```

## Step 2: Deploy Inference Backend

Deploy a sample inference backend that will serve as your inference endpoints:

```bash
kubectl apply -f https://github.com/kubernetes-sigs/gateway-api-inference-extension/raw/main/config/manifests/vllm/sim-deployment.yaml
```

This creates a simulated vLLM deployment with multiple replicas that can handle inference requests.

> **Note**: This deployment creates the `vllm-llama3-8b-instruct` InferencePool and related resources that are referenced in the HTTPRoute configuration below.

## Step 3: Create InferenceModel

Create an InferenceModel resource to define the model configuration:

```bash
kubectl apply -f https://github.com/kubernetes-sigs/gateway-api-inference-extension/raw/v0.4.0/config/manifests/inferencemodel.yaml
```

## Step 4: Create InferencePool Resources

Deploy the InferencePool and related resources:

```bash
kubectl apply -f https://github.com/kubernetes-sigs/gateway-api-inference-extension/raw/v0.4.0/config/manifests/inferencepool-resources.yaml
```

This creates:

- InferencePool resource defining the endpoint selection criteria
- Endpoint Picker Provider (EPP) deployment for intelligent routing
- Associated services and configurations

## Step 5: Configure Gateway and HTTPRoute

Create a Gateway and HTTPRoute that uses the InferencePool:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: inference-pool-with-httproute
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: inference-pool-with-httproute
  namespace: default
spec:
  gatewayClassName: inference-pool-with-httproute
  listeners:
    - name: http
      protocol: HTTP
      port: 80
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: inference-pool-with-httproute
  namespace: default
spec:
  parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: inference-pool-with-httproute
      namespace: default
  rules:
    - backendRefs:
        - group: inference.networking.x-k8s.io
          kind: InferencePool
          name: vllm-llama3-8b-instruct
          namespace: default
          weight: 1
      matches:
        - path:
            type: PathPrefix
            value: /
      timeouts:
        request: 60s
```

## Step 6: Test the Configuration

Once deployed, you can test the inference routing:

```bash
# Get the Gateway external IP
GATEWAY_IP=$(kubectl get gateway inference-pool-with-httproute -o jsonpath='{.status.addresses[0].value}')

# Send a test inference request
curl -X POST "http://${GATEWAY_IP}/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "Say this is a test"
      }
    ],
    "model": "meta-llama/Llama-3.1-8B-Instruct"
  }'
```

## How It Works

### Request Processing Flow

1. **Client Request**: Client sends inference request to the Gateway
2. **Route Matching**: HTTPRoute matches the request based on path prefix
3. **InferencePool Resolution**: Envoy Gateway resolves the InferencePool backend reference
4. **Endpoint Selection**: Endpoint Picker Provider (EPP) selects the optimal endpoint
5. **Request Forwarding**: Request is forwarded to the selected inference backend
6. **Response Return**: Response is returned to the client

### Endpoint Selection

The Endpoint Picker Provider uses various metrics to select the best endpoint:

- **Current Load**: Number of active requests per endpoint
- **Response Time**: Historical response time metrics
- **Availability**: Health status of each endpoint
- **Resource Utilization**: CPU and memory usage of inference pods

### Load Balancing

InferencePool provides intelligent load balancing that goes beyond simple round-robin:

- **Weighted Selection**: Endpoints with better performance get more traffic
- **Adaptive Routing**: Selection criteria adapt based on real-time metrics
- **Failure Handling**: Automatic failover to healthy endpoints

## Configuration Options

### InferencePool Configuration

The InferencePool resource supports various configuration options:

```yaml
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: my-inference-pool
  namespace: default
spec:
  # Port number for inference endpoints
  targetPortNumber: 8000
  
  # Selector for inference pods
  selector:
    app: my-inference-app
  
  # Endpoint Picker Provider configuration
  extensionRef:
    name: my-epp-service
    port: 9002
    failureMode: FailClose
```

### HTTPRoute Timeouts

Configure appropriate timeouts for inference requests:

```yaml
timeouts:
  request: 300s  # Longer timeout for complex inference tasks
  backendRequest: 240s  # Backend-specific timeout
```

## Monitoring and Observability

### Metrics

Monitor your InferencePool deployment using these key metrics:

- **Request Rate**: Number of inference requests per second
- **Response Time**: Latency of inference requests
- **Error Rate**: Percentage of failed requests
- **Endpoint Health**: Status of individual inference endpoints

### Logging

Enable detailed logging to troubleshoot issues:

```bash
# Check Gateway logs
kubectl logs -l gateway.envoyproxy.io/owning-gateway-name=inference-pool-with-httproute

# Check EPP logs
kubectl logs -l app=vllm-llama3-8b-instruct-epp
```

## Troubleshooting

### Common Issues

1. **No Healthy Endpoints**: Check if inference pods are running and healthy
2. **Timeout Errors**: Increase request timeout values
3. **Route Not Found**: Verify HTTPRoute configuration and Gateway status
4. **EPP Connection Issues**: Check EPP service and port configuration

### Debugging Commands

```bash
# Check Gateway status
kubectl describe gateway inference-pool-with-httproute

# Check HTTPRoute status
kubectl describe httproute inference-pool-with-httproute

# Check InferencePool status
kubectl describe inferencepool vllm-llama3-8b-instruct

# Check EPP pod logs
kubectl logs -l app=vllm-llama3-8b-instruct-epp
```

## Next Steps

- Explore [AIGatewayRoute + InferencePool](./aigatewayroute-inferencepool.md) for advanced AI-specific features
- Learn about [custom endpoint picker providers](./custom-epp.md) for specialized routing logic
- Review [monitoring and observability](../observability/) best practices for inference workloads
