---
id: inferencepool-support
title: InferencePool Support
sidebar_position: 1
---

# InferencePool Support

InferencePool is a powerful feature that enables intelligent routing and load balancing for AI inference workloads. It integrates with the [Gateway API Inference Extension](https://gateway-api-inference-extension.sigs.k8s.io/) to provide dynamic endpoint selection based on real-time metrics and availability.

## What is InferencePool?

InferencePool is a Kubernetes custom resource that defines a pool of inference endpoints and specifies how requests should be routed among them. It solves several key challenges in AI inference deployments:

- **Dynamic Load Balancing**: Automatically distribute requests across multiple inference instances
- **Intelligent Routing**: Route requests to the most suitable endpoints based on current load, latency, and availability
- **Automatic Failover**: Handle endpoint failures gracefully with built-in retry mechanisms
- **Resource Optimization**: Maximize utilization of inference compute resources

## Core Problems Solved

### 1. Static Load Balancing Limitations
Traditional load balancing treats all backends equally, but inference workloads have unique characteristics:
- Variable processing times based on model complexity and input size
- Different resource requirements for different models
- Need for intelligent routing based on real-time metrics

### 2. Manual Endpoint Management
Without InferencePool, managing multiple inference endpoints requires:
- Manual configuration updates when endpoints change
- Complex routing logic in application code
- Difficult failover and recovery procedures

### 3. Suboptimal Resource Utilization
Static routing can lead to:
- Uneven load distribution across inference instances
- Underutilized resources while some endpoints are overloaded
- Poor overall system performance and efficiency

## Request Flow

InferencePool supports two integration modes with different request flows:

### AIGatewayRoute + InferencePool Flow

```mermaid
sequenceDiagram
    participant Client as Client (OpenAI SDK)
    participant Envoy as Envoy Proxy
    participant RLS as Rate Limit Service
    participant Processor as AI Gateway External Processor
    participant EPP as Endpoint Picker Provider
    participant Backend as Inference Backend

    Client->>Envoy: Request with model header
    Envoy->>RLS: Check Rate Limit
    RLS-->>Envoy: Rate limit OK
    Envoy->>Processor: Router-level ExtProc Request
    Note over Processor: Extract Model Name & Routing
    Processor-->>Envoy: ClearRouteCache
    Envoy->>EPP: Router-level ExtProc Request
    Note over EPP: Pick Optimal Endpoint
    EPP-->>Envoy: Add x-gateway-destination-endpoint header
    
    loop Retry/Fallback loop
    Note over Envoy: Forward to Selected Endpoint
    Envoy->>Processor: Upstream-level ExtProc Request
    Note over Processor: Request Transform
    Processor-->>Envoy: Transformed Request
    Envoy->>Backend: Forward Request
    Backend-->>Envoy: Response
    end
    
    Envoy->>Processor: Process Response
    Note over Processor: Extract Token Usage & Metrics
    Processor-->>Envoy: Add Usage Metadata
    Envoy->>RLS: Update Rate Limit Budget
    RLS-->>Envoy: Budget Updated
    Envoy->>Client: Response
```

### HTTPRoute + InferencePool Flow

```mermaid
sequenceDiagram
    participant Client as Client
    participant Envoy as Envoy Proxy
    participant EPP as Endpoint Picker Provider
    participant Backend as Inference Backend

    Client->>Envoy: Request
    Envoy->>EPP: Router-level ExtProc Request
    Note over EPP: Pick Optimal Endpoint
    EPP-->>Envoy: Add x-gateway-destination-endpoint header
    
    loop Retry/Fallback loop
    Note over Envoy: Forward to Selected Endpoint
    Envoy->>Backend: Forward Request
    Backend-->>Envoy: Response
    end
    
    Envoy->>Client: Response
```

## Integration Approaches

Envoy AI Gateway supports two ways to use InferencePool:

### 1. HTTPRoute + InferencePool
Direct integration with standard Gateway API HTTPRoute for simple inference routing scenarios.

**Use Cases:**
- Simple inference workloads without complex AI-specific requirements
- Direct OpenAI-compatible API forwarding
- Basic load balancing across inference endpoints

### 2. AIGatewayRoute + InferencePool  
Enhanced integration with AI Gateway's custom route type for advanced AI-specific features.

**Use Cases:**
- Multi-model routing based on request content
- Token-based rate limiting
- Advanced observability and metrics
- Request/response transformation
- Integration with AI Gateway's security policies

## AIGatewayRoute Advantages

AIGatewayRoute provides several advantages over standard HTTPRoute when used with InferencePool:

### Native OpenAI Support
- **Automatic Model Extraction**: Parse model names from request body automatically
- **Dynamic Routing**: Route to different InferencePools based on model in the same listener
- **Schema Validation**: Built-in OpenAI API schema validation and transformation

### Token-Based Rate Limiting
- **Usage Tracking**: Extract token usage from inference responses
- **Dynamic Budgets**: Adjust rate limits based on actual token consumption
- **Cost Management**: Implement cost-based rate limiting for different models

### Advanced Observability
- **AI-Specific Metrics**: Detailed metrics for inference requests, token usage, and model performance
- **Request Tracing**: Enhanced tracing with AI-specific context
- **Custom Dashboards**: Pre-built dashboards for AI workload monitoring

### Enhanced Security
- **Backend Security Policies**: Integration with AI Gateway's security framework
- **API Key Management**: Centralized management of upstream API keys
- **Request Validation**: Advanced request validation and sanitization

## Getting Started

To use InferencePool with Envoy AI Gateway, you'll need to:

1. **Install Prerequisites**: Deploy Gateway API Inference Extension
2. **Configure InferencePool**: Define your inference endpoints and selection criteria
3. **Set up Routes**: Configure either HTTPRoute or AIGatewayRoute to use the InferencePool
4. **Deploy Endpoint Picker**: Install and configure the endpoint picker provider

The following sections provide detailed guides for both integration approaches with complete examples and step-by-step instructions.
