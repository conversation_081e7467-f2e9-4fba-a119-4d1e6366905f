---
id: custom-epp
title: Custom Endpoint Picker Providers
sidebar_position: 4
---

# Custom Endpoint Picker Providers

While the Gateway API Inference Extension provides a default Endpoint Picker Provider (EPP), you can implement custom EPP solutions to meet specific routing requirements for your inference workloads.

## What is an Endpoint Picker Provider?

An Endpoint Picker Provider (EPP) is an external service that implements intelligent endpoint selection logic for InferencePool. It receives requests from Envoy and determines which specific endpoint should handle each request based on various criteria.

## EPP Requirements

To create a custom EPP, your implementation must satisfy these technical requirements:

### 1. External Processing Server
Your EPP must implement the [Envoy external processing (ext-proc) gRPC protocol](https://www.envoyproxy.io/docs/envoy/latest/api-v3/extensions/filters/http/ext_proc/v3/ext_proc.proto).

### 2. Endpoint Selection Header
The EPP must add the `x-gateway-destination-endpoint` header to request headers containing the selected endpoint's IP address or hostname.

### 3. InferencePool API Awareness
Your EPP should understand InferencePool resource specifications to:
- Enable Envoy AI Gateway to establish ext-proc server connections
- Determine which routes require ext-proc filter attachment
- Configure Original Destination clusters with header-based routing

## Implementation Guide

### Basic EPP Structure

Here's a basic structure for a custom EPP implementation:

```go
package main

import (
    "context"
    "fmt"
    "net"
    
    extprocv3 "github.com/envoyproxy/go-control-plane/envoy/service/ext_proc/v3"
    "google.golang.org/grpc"
)

type CustomEPP struct {
    // Your custom logic fields
    poolName      string
    poolNamespace string
    // Add your custom selection criteria
}

func (epp *CustomEPP) Process(
    stream extprocv3.ExternalProcessor_ProcessServer,
) error {
    for {
        req, err := stream.Recv()
        if err != nil {
            return err
        }
        
        // Process the request and select endpoint
        response := epp.processRequest(req)
        
        if err := stream.Send(response); err != nil {
            return err
        }
    }
}

func (epp *CustomEPP) processRequest(
    req *extprocv3.ProcessingRequest,
) *extprocv3.ProcessingResponse {
    // Implement your endpoint selection logic here
    selectedEndpoint := epp.selectEndpoint(req)
    
    // Add the selected endpoint to headers
    return &extprocv3.ProcessingResponse{
        Response: &extprocv3.ProcessingResponse_RequestHeaders{
            RequestHeaders: &extprocv3.HeadersResponse{
                Response: &extprocv3.CommonResponse{
                    HeaderMutation: &extprocv3.HeaderMutation{
                        SetHeaders: []*extprocv3.HeaderValueOption{
                            {
                                Header: &extprocv3.HeaderValue{
                                    Key:   "x-gateway-destination-endpoint",
                                    Value: selectedEndpoint,
                                },
                            },
                        },
                    },
                },
            },
        },
    }
}

func (epp *CustomEPP) selectEndpoint(
    req *extprocv3.ProcessingRequest,
) string {
    // Implement your custom endpoint selection logic
    // This could be based on:
    // - Request content analysis
    // - Load balancing algorithms
    // - Custom metrics
    // - Business logic
    
    return "192.168.1.100:8000" // Example endpoint
}
```

### Deployment Configuration

Deploy your custom EPP as a Kubernetes service:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: custom-epp
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: custom-epp
  template:
    metadata:
      labels:
        app: custom-epp
    spec:
      containers:
        - name: epp
          image: your-registry/custom-epp:latest
          ports:
            - containerPort: 9002
          args:
            - -poolName
            - "your-pool-name"
            - -poolNamespace
            - "default"
            - -grpcPort
            - "9002"
---
apiVersion: v1
kind: Service
metadata:
  name: custom-epp
  namespace: default
spec:
  selector:
    app: custom-epp
  ports:
    - protocol: TCP
      port: 9002
      targetPort: 9002
      appProtocol: http2
  type: ClusterIP
```

### InferencePool Configuration

Reference your custom EPP in the InferencePool:

```yaml
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: custom-inference-pool
  namespace: default
spec:
  targetPortNumber: 8000
  selector:
    app: your-inference-app
  extensionRef:
    name: custom-epp  # Reference your custom EPP service
    port: 9002
    failureMode: FailClose
```

## Custom Selection Strategies

### Load-Based Selection

Implement endpoint selection based on current load:

```go
func (epp *CustomEPP) selectEndpointByLoad() string {
    endpoints := epp.getAvailableEndpoints()
    
    var bestEndpoint string
    var lowestLoad float64 = math.MaxFloat64
    
    for _, endpoint := range endpoints {
        load := epp.getCurrentLoad(endpoint)
        if load < lowestLoad {
            lowestLoad = load
            bestEndpoint = endpoint
        }
    }
    
    return bestEndpoint
}
```

### Content-Based Selection

Route based on request content analysis:

```go
func (epp *CustomEPP) selectEndpointByContent(
    req *extprocv3.ProcessingRequest,
) string {
    // Extract request body or headers
    body := epp.extractRequestBody(req)
    
    // Analyze content (e.g., model type, complexity)
    if epp.isComplexRequest(body) {
        return epp.getHighPerformanceEndpoint()
    }
    
    return epp.getStandardEndpoint()
}
```

### Geographic Selection

Route based on client location:

```go
func (epp *CustomEPP) selectEndpointByLocation(
    req *extprocv3.ProcessingRequest,
) string {
    clientIP := epp.extractClientIP(req)
    region := epp.getRegionFromIP(clientIP)
    
    return epp.getNearestEndpoint(region)
}
```

## Integration Workflow

The integration process for custom EPP implementations:

1. **Development**: Implement your EPP following the ext-proc protocol
2. **Deployment**: Deploy EPP service in your Kubernetes cluster
3. **Configuration**: Create InferencePool referencing your EPP service
4. **Route Binding**: Link InferencePool to HTTPRoute or AIGatewayRoute
5. **Testing**: Verify endpoint selection behavior

## Best Practices

### Performance Optimization

- **Connection Pooling**: Reuse connections to inference endpoints
- **Caching**: Cache endpoint health and performance metrics
- **Async Processing**: Use asynchronous operations for external calls
- **Timeout Handling**: Implement appropriate timeouts for all operations

### Reliability

- **Health Checking**: Continuously monitor endpoint health
- **Graceful Degradation**: Handle endpoint failures gracefully
- **Circuit Breaker**: Implement circuit breaker patterns for failing endpoints
- **Retry Logic**: Add retry mechanisms for transient failures

### Observability

- **Metrics**: Expose metrics for endpoint selection decisions
- **Logging**: Log selection criteria and decisions
- **Tracing**: Support distributed tracing for request flows
- **Debugging**: Provide debugging endpoints for troubleshooting

### Security

- **Authentication**: Secure communication with inference endpoints
- **Authorization**: Implement proper access controls
- **Input Validation**: Validate all incoming requests
- **Rate Limiting**: Protect against abuse and overload

## Monitoring Custom EPP

### Key Metrics

Monitor these metrics for your custom EPP:

- **Selection Latency**: Time taken to select endpoints
- **Endpoint Health**: Status of available endpoints
- **Selection Distribution**: How requests are distributed across endpoints
- **Error Rates**: Failed endpoint selections and reasons

### Health Checks

Implement health check endpoints:

```go
func (epp *CustomEPP) healthCheck(w http.ResponseWriter, r *http.Request) {
    if epp.isHealthy() {
        w.WriteHeader(http.StatusOK)
        w.Write([]byte("OK"))
    } else {
        w.WriteHeader(http.StatusServiceUnavailable)
        w.Write([]byte("Unhealthy"))
    }
}
```

## Troubleshooting

### Common Issues

1. **EPP Not Responding**: Check service connectivity and port configuration
2. **Invalid Headers**: Verify `x-gateway-destination-endpoint` header format
3. **Endpoint Selection Errors**: Review selection logic and endpoint availability
4. **Performance Issues**: Monitor EPP response times and optimize selection algorithms

### Debugging Commands

```bash
# Check EPP pod status
kubectl get pods -l app=custom-epp

# Check EPP logs
kubectl logs -l app=custom-epp

# Test EPP connectivity
kubectl port-forward svc/custom-epp 9002:9002

# Check InferencePool status
kubectl describe inferencepool custom-inference-pool
```

## Examples and Templates

For complete examples and templates for custom EPP implementations, refer to:

- [Gateway API Inference Extension Examples](https://github.com/kubernetes-sigs/gateway-api-inference-extension/tree/main/examples)
- [EPP Implementation Templates](https://github.com/kubernetes-sigs/gateway-api-inference-extension/tree/main/pkg/epp)

## Next Steps

- Review the [Gateway API Inference Extension documentation](https://gateway-api-inference-extension.sigs.k8s.io/)
- Explore [advanced routing scenarios](./aigatewayroute-inferencepool.md)
- Learn about [observability best practices](../observability/) for inference workloads
