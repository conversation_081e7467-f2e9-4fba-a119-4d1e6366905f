# Envoy AI Gateway Examples

This directory contains various examples demonstrating different features and use cases of Envoy AI Gateway.


### [Basic Setup](./basic/)
A comprehensive example showing how to set up Envoy AI Gateway with multiple providers including OpenAI, AWS Bedrock, and Azure OpenAI.

## Advanced Features

### [Provider Fallback](./provider_fallback/)
Shows how to configure automatic failover between multiple AI providers for high availability.

### [Token Rate Limiting](./token_ratelimit/)
Demonstrates usage-based rate limiting to control costs and prevent abuse.

### [Monitoring](./monitoring/)
Example setup for comprehensive monitoring and observability with Prometheus and Grafana.


### [Custom Metrics](./extproc_custom_metrics/)
Example of implementing custom metrics collection using the external processor interface.

