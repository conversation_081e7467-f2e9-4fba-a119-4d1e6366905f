name: Build and Test
on:
  pull_request:
    branches:
      - 'main'
      # Release branches are like "release/v0.1", "release/v0.2", etc. where we backport the changes to non EOL versions.
      # The branch will be created from the main branch after the initial release tag is cut. For example, when we cut v0.8.0 release,
      # we will create a branch "release/v0.8" from the main branch. For rc release, we simply iterate on main branch.
      #
      # See RELEASES.md for more details.
      - 'release/**'
    paths-ignore:
      - '**/*.md'
      - 'site/**'
      - 'netlify.toml'

  push:
    branches:
      - 'main'
      - 'release/**'
    paths-ignore:
      - '**/*.md'
      - 'site/**'
      - 'netlify.toml'

concurrency:
  # https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#example-using-concurrency-to-cancel-any-in-progress-job-or-run
  group: ${{ github.ref }}-${{ github.workflow }}-${{ github.actor }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read
  packages: write

jobs:
  style:
    name: Check
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/.cache/golangci-lint
            ~/go/pkg/mod
            ~/go/bin
          key: code-style-check-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - name: Ensure `make precommit` is executed
        run: make check
  unittest:
    name: Unit Test
    needs: [style]
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: unittest-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}-${{ matrix.os }}

      # This runs ollama server to be used in `aigw run` end-to-end tests.
      # The test case using it will be skipped if ollama is not available.
      # Since installing it and pulling the model takes a while, we do it only for Linux runners.
      - name: Start Ollama server
        if: matrix.os == 'ubuntu-latest'
        run: |
          curl -fsSL https://ollama.com/install.sh | sh
          sleep 10 # Wait for ollama to start. TODO: this is really hacky, so find a better way to wait for ollama to start. This can be a source of flakiness.
          ollama pull qwen3:0.6b

      - env:
          TEST_AWS_ACCESS_KEY_ID: ${{ secrets.AWS_BEDROCK_USER_AWS_ACCESS_KEY_ID }}
          TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_BEDROCK_USER_AWS_SECRET_ACCESS_KEY }}
          TEST_OPENAI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_OPENAI_API_KEY }}
        run: make test-coverage

  test_crdcel:
    name: CRD CEL Validation Test
    needs: [style]
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: celvalidation-test-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}-${{ matrix.os }}
      - run: make test-crdcel

  test_controller:
    name: Controller Test
    needs: [style]
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: controller-test-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}-${{ matrix.os }}
      - run: make test-controller

  test_extproc:
    name: External Processor Test (Envoy v${{ matrix.version }} on ${{ matrix.os }})
    needs: [style]
    strategy:
      fail-fast: false
      matrix:
        # Note: we cannot run the latest Envoy version on macOS due to https://github.com/tetratelabs/archive-envoy/issues/12.
        # Once it's supported, the following "binary installation" steps below can be just removed and
        # we can simply exec.Cmd with "go tool func-e run" with the envoy version configured via ENVOY_VERSION env var.
        include:
          - version: 1.34.1  # NOTE: when updating this, also update the comment in the CONTRIBUTING.md file.
            os: ubuntu-latest
          - version: 1.34.1  # NOTE: when updating this, also update the comment in the CONTRIBUTING.md file.
            os: macos-latest
          - version: latest
            os: ubuntu-latest
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/bin
          key: extproc-tests-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - name: Install stable Envoy via func-e
        if: matrix.version != 'latest'
        run: |
          go tool func-e use ${{ matrix.version }}
          echo $HOME/.func-e/versions/${{ matrix.version }}/bin >> $GITHUB_PATH
      - name: Install latest Envoy
        if: matrix.version == 'latest'
        run: |
          export ENVOY_BIN_DIR=$HOME/envoy/bin
          mkdir -p $ENVOY_BIN_DIR
          docker run -v $ENVOY_BIN_DIR:/tmp/ci -w /tmp/ci \
          --entrypoint /bin/cp envoyproxy/envoy-dev:latest /usr/local/bin/envoy .
          echo $ENVOY_BIN_DIR >> $GITHUB_PATH
      - env:
          TEST_AWS_ACCESS_KEY_ID: ${{ secrets.AWS_BEDROCK_USER_AWS_ACCESS_KEY_ID }}
          TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_BEDROCK_USER_AWS_SECRET_ACCESS_KEY }}
          TEST_OPENAI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_OPENAI_API_KEY }}
          TEST_GEMINI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_GEMINI_API_KEY }}
          TEST_GROQ_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_GROQ_API_KEY }}
          TEST_GROK_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_GROK_API_KEY }}
          TEST_SAMBANOVA_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_SAMBANOVA_API_KEY }}
          TEST_DEEPINFRA_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_DEEPINFRA_API_KEY }}
        run: make test-extproc

  test_e2e:
    # Not all the cases in E2E require secrets, so we run for all the events.
    name: E2E Test (Envoy Gateway ${{ matrix.name }})
    # TODO: make it possible to run this job on macOS as well, which is a bit tricky due to the nested
    # virtualization is not supported on macOS runners.
    # E.g. Use https://github.com/douglascamata/setup-docker-macos-action  per the comment in
    # https://github.com/actions/runner-images/issues/17#issuecomment-1971073406
    needs: [style]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: latest
            envoy_gateway_version: v0.0.0-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/.cache/golangci-lint
            ~/go/pkg/mod
            ~/go/bin
          key: e2e-test-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}
      - uses: docker/setup-buildx-action@v3
      - env:
          EG_VERSION: ${{ matrix.envoy_gateway_version }}
          TEST_AWS_ACCESS_KEY_ID: ${{ secrets.AWS_BEDROCK_USER_AWS_ACCESS_KEY_ID }}
          TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_BEDROCK_USER_AWS_SECRET_ACCESS_KEY }}
          TEST_OPENAI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_OPENAI_API_KEY }}
          TEST_GEMINI_API_KEY: ${{ secrets.ENVOY_AI_GATEWAY_GEMINI_API_KEY }}
        run: make test-e2e

  docker_push:
    # Docker builds are verified in test_e2e job, so we only need to push the images when the event is a push event.
    if: github.event_name == 'push'
    name: Push Docker Images
    needs: [unittest, test_crdcel, test_controller, test_extproc, test_e2e]
    uses: ./.github/workflows/docker_build_job.yaml
    secrets: inherit

  helm_push:
    name: Push Helm chart
    # Only push the Helm chart to the GHR when merged into the main branch.
    if: github.event_name == 'push'
    needs: [docker_push]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Login into DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}
      - run: |
          make helm-push HELM_CHART_VERSION=v0.0.0-latest
          make helm-push HELM_CHART_VERSION=0.0.0-latest
          make helm-push HELM_CHART_VERSION=v0.0.0-${{ github.sha }} TAG=${{ github.sha }}
          make helm-push HELM_CHART_VERSION=0.0.0-${{ github.sha }} TAG=${{ github.sha }}
