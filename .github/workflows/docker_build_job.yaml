name: Docker Build Job

on:
  workflow_call:

jobs:
  docker_builds:
    name: ${{ matrix.command_name }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - command_name: "controller"
            cmd_path_prefix: cmd
          - command_name: "extproc"
            cmd_path_prefix: cmd
          - command_name: "testupstream"
            cmd_path_prefix: tests/internal/testupstreamlib
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          cache: false
          go-version-file: go.mod
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/.cache/golangci-lint
            ~/go/pkg/mod
            ~/go/bin
          key: build-container-${{ hashFiles('**/go.mod', '**/go.sum', '**/Makefile') }}

      - uses: docker/setup-buildx-action@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@49b3bc8e6bdd4a60e6116a5414239cba5943d3cf  # v3.2.0

      - name: Set up Docker buildx
        id: buildx
        uses: docker/setup-buildx-action@988b5a0280414f521da01fcc63a27aeeb4b104db  # v3.6.1

      - name: Login into DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      # Push images for the push events, e.g. when a new tag is pushed as well as PR merges.
      # * Only use the tag if the event is a tag event, otherwise use the hash.
      # * Build for both amd64 and arm64 platforms.
      - name: Build and Push Image
        run: |
          if [[ "$GITHUB_REF" == refs/tags/* ]]; then
            TAG="${GITHUB_REF#refs/tags/}"
          else
            TAG="${{ github.sha }}"
          fi
          make docker-build.${{ matrix.command_name }} CMD_PATH_PREFIX=${{ matrix.cmd_path_prefix }}  ENABLE_MULTI_PLATFORMS=true TAG=$TAG DOCKER_BUILD_ARGS="--push"

      - name: Build and Push Image Latest
        run: |
          if [[ "$GITHUB_REF" != refs/tags/* ]]; then
            make docker-build.${{ matrix.command_name }} CMD_PATH_PREFIX=${{ matrix.cmd_path_prefix }}  ENABLE_MULTI_PLATFORMS=true TAG="latest" DOCKER_BUILD_ARGS="--push"
          fi
