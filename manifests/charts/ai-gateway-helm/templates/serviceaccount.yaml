# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

{{ if .Values.controller.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "ai-gateway-helm.controller.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "ai-gateway-helm.labels" . | nindent 4 }}
  {{- with .Values.controller.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "ai-gateway-helm.controller.serviceAccountName" . }}
rules:
  - apiGroups: [""]
    resources:
      - secrets
      - pods # TODO: this can be limited to EG system namespace, not the cluster level.
    verbs:
      - '*'
  - apiGroups: ["apps"]
    resources:
      - deployments # TODO: this can be limited to EG system namespace, not the cluster level.
      - daemonsets # TODO: this can be limited to EG system namespace, not the cluster level.
    verbs:
      - '*'
  - apiGroups:
      - inference.networking.x-k8s.io
    resources:
      - '*'
    verbs:
      - '*'
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - '*'
    verbs:
      - '*'
  - apiGroups:
      - aigateway.envoyproxy.io
    resources:
      - '*'
    verbs:
      - '*'
  - apiGroups:
      - gateway.envoyproxy.io
    resources:
      - '*'
    verbs:
      - '*'
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - get
      - watch
      - list
      - create
      - update
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - mutatingwebhookconfigurations
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - mutatingwebhookconfigurations
    resourceNames:
      - 'envoy-ai-gateway-gateway-pod-mutator.{{ .Release.Namespace }}'
    verbs:
      - update
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "ai-gateway-helm.controller.serviceAccountName" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "ai-gateway-helm.controller.serviceAccountName" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "ai-gateway-helm.controller.serviceAccountName" . }}
    namespace: '{{ .Release.Namespace }}'
{{- end }}
