# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: apple
  namespace: default
spec:
  targetRefs:
    - name: some-gateway
      kind: Gateway
      group: gateway.networking.k8s.io
  parentRefs:
    - name: some-gateway
      kind: Gateway
      group: gateway.networking.k8s.io
  schema:
    name: OpenAI
  rules:
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: llama3-70b
      backendRefs:
        - name: kserve
