# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

# This should fail validation: group and kind must be specified together

apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: inference-pool-partial-ref
  namespace: default
spec:
  parentRefs:
    - name: some-gateway
      kind: Gateway
      group: gateway.networking.k8s.io
  schema:
    name: OpenAI
  rules:
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: llama3-8b
      backendRefs:
        - name: vllm-llama3-8b-instruct
          group: inference.networking.x-k8s.io
          # Missing kind field
