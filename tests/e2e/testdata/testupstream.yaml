# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: translation-testupstream
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: translation-testupstream
  namespace: default
spec:
  gatewayClassName: translation-testupstream
  listeners:
    - name: http
      protocol: HTTP
      port: 80
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: translation-testupstream
  namespace: default
spec:
  schema:
    name: OpenAI
  targetRefs:
    - name: translation-testupstream
      kind: Gateway
      group: gateway.networking.k8s.io
  rules:
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: some-cool-model
      backendRefs:
        - name: translation-testupstream-cool-model-backend
          weight: 100
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: another-cool-model
      backendRefs:
        - name: translation-testupstream-another-cool-model-backend
          weight: 100
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: translation-testupstream-cool-model-backend
  namespace: default
spec:
  schema:
    name: OpenAI
  backendRef:
    name: testupstream
    kind: Backend
    group: gateway.envoyproxy.io
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: translation-testupstream-another-cool-model-backend
  namespace: default
spec:
  schema:
    name: AWSBedrock
  backendRef:
    name: testupstream-canary
    kind: Backend
    group: gateway.envoyproxy.io
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: testupstream
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: testupstream.default.svc.cluster.local
        port: 80
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: testupstream-canary
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: testupstream-canary.default.svc.cluster.local
        port: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: testupstream
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: testupstream
  template:
    metadata:
      labels:
        app: testupstream
    spec:
      containers:
        - name: testupstream
          image: docker.io/envoyproxy/ai-gateway-testupstream:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
          env:
            - name: TESTUPSTREAM_ID
              value: primary
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 1
---
apiVersion: v1
kind: Service
metadata:
  name: testupstream
  namespace: default
spec:
  selector:
    app: testupstream
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: testupstream-canary
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: testupstream-canary
  template:
    metadata:
      labels:
        app: testupstream-canary
    spec:
      containers:
        - name: testupstream-canary
          image: docker.io/envoyproxy/ai-gateway-testupstream:latest
          imagePullPolicy: IfNotPresent
          env:
            - name: TESTUPSTREAM_ID
              value: canary
          ports:
            - containerPort: 8080
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 1
---
apiVersion: v1
kind: Service
metadata:
  name: testupstream-canary
  namespace: default
spec:
  selector:
    app: testupstream-canary
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
