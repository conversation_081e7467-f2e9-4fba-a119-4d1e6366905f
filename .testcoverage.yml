# This is the configuration file for https://github.com/vladopajic/go-test-coverage

profile: ./out/go-test-coverage.out
local-prefix: "github.com/envoyproxy/ai-gateway/"

threshold:
  file: 70
  # TODO: increase to 90.
  package: 81
  # TODO: increase to 95.
  total: 83

exclude:
  paths:
    # Examples are not part of the main codebase.
    - ^examples/
    # Main functions are always tested with integration tests.
    - cmd/
    # Generated code should not be tested.
    - zz_generated.deepcopy.go
    # They are test-only libraries.
    - tests/internal/envtest.go
    - internal/testing/
    # TODO: Remove this exclusion.
    - internal/controller/controller.go
